所有终端输出已重定向到: ../model/BraTS19_temporal_ddnet_DD-Net-SAM2-adapter-EMA-Switch-20000_25_labeled/all_output.txt
====== 日志系统配置完成 - 包含文件输出 ======
依赖安装完成
[17:01:16.542] 未找到适配器特定权重文件，将使用标准权重并初始化适配器参数
[17:01:16.542] ============= 训练开始 =============
[17:01:16.542] 日志文件保存在: ../model/BraTS19_temporal_ddnet_DD-Net-SAM2-adapter-EMA-Switch-20000_25_labeled/log.txt
[17:01:16.543] Namespace(dataset='BraTS19', root_path='../data/BraTS19', num_classes=2, exp='DD-Net-SAM2-adapter-EMA-Switch-20000', model='temporal_ddnet', max_iterations=30000, batch_size=24, deterministic=1, base_lr=0.01, patch_size=[256, 256], seed=1337, labeled_bs=12, labeled_num=25, gpu='0', consistency=0.1, ema_decay=0.99, consistency_rampup=200.0, temperature=0.2, lamda=1, beta=0.3, temp=1, sam2_model_id='base', sam2_weight='/mnt/sda/zzh/DD-Net/sam2/checkpoints/sam2.1_hiera_base_plus.pt', sam2_weight_loss=0.2, sam2_confidence=0.8, sam2_frequency=1, use_ordered_slices=0, use_adapter=1, adapter_scale_factor=32, adapter_img_size=256, adapter_patch_size=16, teacher_switch_iter=20000, use_ema_teacher=1, quality_eval_freq=100)
[17:01:16.543] Loading SAM2 model from local file: /mnt/sda/zzh/DD-Net/sam2/checkpoints/sam2.1_hiera_base_plus.pt
设置SAM2适配器为训练模式...
【设置模块为训练模式】: prompt_generator
【设置模块为训练模式】: prompt_generator.shared_mlp
【设置模块为训练模式】: prompt_generator.embedding_generators
【设置模块为训练模式】: prompt_generator.embedding_generators.0
【设置模块为训练模式】: prompt_generator.embedding_generators.1
【设置模块为训练模式】: prompt_generator.embedding_generators.2
【设置模块为训练模式】: prompt_generator.embedding_generators.3
【设置模块为训练模式】: prompt_generator.lightweight_mlps
【设置模块为训练模式】: prompt_generator.lightweight_mlps.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.0.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.0.1
【设置模块为训练模式】: prompt_generator.lightweight_mlps.1
【设置模块为训练模式】: prompt_generator.lightweight_mlps.1.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.1.1
【设置模块为训练模式】: prompt_generator.lightweight_mlps.2
【设置模块为训练模式】: prompt_generator.lightweight_mlps.2.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.2.1
【设置模块为训练模式】: prompt_generator.lightweight_mlps.3
【设置模块为训练模式】: prompt_generator.lightweight_mlps.3.0
【设置模块为训练模式】: prompt_generator.lightweight_mlps.3.1
【设置模块为训练模式】: prompt_generator.prompt_generator
【设置模块为训练模式】: prompt_generator.prompt_generator.0
【设置模块为训练模式】: prompt_generator.prompt_generator.1
【设置模块为训练模式】: prompt_generator.prompt_generator.2
【设置模块为训练模式】: prompt_generator.prompt_generator.3
SAM2适配器参数已设置为可训练
[17:01:17.213] SAM2 model loaded successfully
[17:01:17.246] EMA教师模型已创建
[17:01:17.246] Using random slices dataset
